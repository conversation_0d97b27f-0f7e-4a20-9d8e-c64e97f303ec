#!/usr/bin/env python3
"""
Automated Distance Measurement Tool with Computer Vision and Auto-Clicking

This tool combines computer vision object detection with automated clicking to measure
distances between objects in screenshots. It integrates with the existing captcha solver
infrastructure for maximum accuracy and reliability.

Features:
- Automatic screenshot capture using existing captcha solver coordinates
- Grok Vision API integration for object detection
- Automated clicking on detected objects using pyautogui
- Precise distance calculation between clicked points
- Full automation with detailed logging

Usage:
    python automated_distance_tool.py [--mode auto|manual] [--objects "object1,object2"]

Examples:
    python automated_distance_tool.py --mode auto --objects "fox,robot"
    python automated_distance_tool.py --mode manual  # Interactive mode
"""

import requests
import json
import base64
import pyautogui
from PIL import ImageGrab
import time
import math
import argparse
import logging
import os
import sys
from logging.handlers import RotatingFileHandler
from typing import Tuple, Optional, Dict, Any
from random import uniform
from api_key_manager import get_api_manager


class UnicodeStreamHandler(logging.StreamHandler):
    """Custom StreamHandler that handles Unicode characters properly on Windows"""

    def emit(self, record):
        try:
            msg = self.format(record)
            # Replace Unicode emojis with ASCII equivalents for console output
            msg = msg.replace('✅', '[OK]').replace('❌', '[FAIL]').replace('⚠️', '[WARN]')
            stream = self.stream
            stream.write(msg + self.terminator)
            self.flush()
        except Exception:
            self.handleError(record)


def setup_logging():
    """
    Setup centralized logging configuration for the AI module.
    This ensures the AI module uses the same logging configuration and writes to the correct file.
    """
    # Clear any existing handlers to avoid conflicts
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Create file handler with rotation (supports Unicode)
    log_file = 'gmx_creator.log'
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'  # Ensure file handler supports Unicode
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)

    # Create console handler with Unicode support
    console_handler = UnicodeStreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # Configure root logger
    logging.root.setLevel(logging.DEBUG)
    logging.root.addHandler(file_handler)
    logging.root.addHandler(console_handler)

    # Prevent duplicate logs from other libraries
    logging.getLogger('selenium').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('PIL').setLevel(logging.WARNING)

    return logging.getLogger(__name__)


class AutomatedDistanceTool:
    def __init__(self):
        # Setup centralized logging
        setup_logging()
        self.logger = logging.getLogger("AutomatedDistanceTool")

        # Use centralized API manager for key rotation and fallback
        self.api_manager = get_api_manager()

        # Use same captcha bounding box for consistency
        #self.captcha_bbox = (492, 381, 826, 645)  # (left, top, right, bottom)
        #For Demo Website
        self.captcha_bbox = (1401, 490, 1731, 751)  # (left, top, right, bottom)
        self.slide_button_absolute = (1442, 714)  # Average from manual measurements

        # Store detected coordinates
        self.detected_objects = {}
        self.clicked_points = []

        self.logger.info("Automated Distance Measurement Tool Initialized")
        self.logger.info(f"Using captcha region: {self.captcha_bbox}")
        self.logger.info(f"Slide button reference: {self.slide_button_absolute}")



    def capture_screenshot(self) -> str:
        """Capture screenshot of the captcha area with debugging info"""
        self.logger.debug("Starting screenshot capture process")

        try:
            screenshot = ImageGrab.grab(bbox=self.captcha_bbox)
            screenshot.save("distance_measurement.png")

            # Add debugging info
            self.logger.info(f"Screenshot captured successfully: {self.captcha_bbox}")
            self.logger.debug(f"Screenshot size: {screenshot.size}")

            # Calculate average brightness for debugging
            try:
                import numpy as np
                img_array = np.array(screenshot)
                avg_brightness = np.mean(img_array)
                self.logger.debug(f"Screenshot average brightness: {avg_brightness:.1f} (0=black, 255=white)")
            except ImportError:
                self.logger.debug("NumPy not available - skipping brightness check")

            return "distance_measurement.png"

        except Exception as e:
            self.logger.error(f"Failed to capture screenshot: {str(e)}")
            raise

    def detect_objects_with_grok(self, image_path: str, target_objects: str = "two main objects") -> Optional[Dict[str, Any]]:
        """Use Grok Vision API to detect and locate objects in the screenshot"""
        self.logger.info(f"Starting object detection for: {target_objects}")

        try:
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')

            self.logger.debug(f"Image encoded to base64, size: {len(base64_image)} characters")

            payload = {
                #"model": "qwen/qwen2.5-vl-32b-instruct",
                "model": "anthropic/claude-3.7-sonnet",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": f"""Analyze this image and identify {target_objects} for distance measurement.

CRITICAL REQUIREMENTS FOR OBJECT DETECTION:
- Identify and locate the two most prominent objects in the image
- Measure the precise pixel coordinates of each object's CENTER point
- Look carefully at the actual object boundaries and find the true geometric center
- Consider the object's visual mass distribution when determining center
- The coordinates should be relative to this image (not the full screen)
- Be extremely accurate - even 5-10 pixel errors can affect distance measurement

COORDINATE PRECISION:
- Examine each object's shape and boundaries carefully
- Calculate the center point based on the object's actual dimensions
- Account for any visual effects, shadows, or transparency
- Provide coordinates as precise integers

Return your response in this exact JSON format:
{{
  "objects": [
    {{
      "name": "object1_name",
      "coordinates": {{"x": precise_x_coordinate, "y": precise_y_coordinate}},
      "description": "brief description of the object"
    }},
    {{
      "name": "object2_name",
      "coordinates": {{"x": precise_x_coordinate, "y": precise_y_coordinate}},
      "description": "brief description of the object"
    }}
  ],
  "image_analysis": "brief description of what you see in the image",
  "confidence": "high|medium|low"
}}

Please provide only the JSON response without any additional text."""
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "temperature": 0.1
            }

            self.logger.info("Sending image to AI Vision API for object detection...")
            response = self.api_manager.make_request("/chat/completions", method="POST", payload=payload)

            if response:
                self.logger.info("Successfully received AI response for object detection")
            else:
                self.logger.error("Failed to get response from AI Vision API")

            return response

        except Exception as e:
            self.logger.error(f"Error in object detection process: {str(e)}")
            return None

    def parse_detection_response(self, response: Dict[str, Any]) -> bool:
        """Parse the AI API response and extract object coordinates"""
        self.logger.debug("Starting to parse AI detection response")

        try:
            content = response["choices"][0]["message"]["content"]
            self.logger.debug(f"AI Response content length: {len(content)} characters")

            # Extract JSON from response
            if "```json" in content:
                json_str = content.split("```json")[1].split("```")[0].strip()
                self.logger.debug("Extracted JSON from markdown code block")
            else:
                json_str = content.strip()
                self.logger.debug("Using raw content as JSON")

            data = json.loads(json_str)
            self.logger.debug("Successfully parsed JSON response")

            # Validate response structure
            if "objects" not in data or len(data["objects"]) < 2:
                self.logger.error("Invalid response: Need at least 2 objects")
                return False

            # Extract object coordinates
            self.detected_objects = {}
            for i, obj in enumerate(data["objects"][:2]):  # Take first 2 objects
                name = obj.get("name", f"object_{i+1}")
                coords = obj.get("coordinates", {})
                description = obj.get("description", "unknown object")

                if "x" not in coords or "y" not in coords:
                    self.logger.error(f"Missing coordinates for {name}")
                    return False

                self.detected_objects[name] = {
                    "coordinates": (coords["x"], coords["y"]),
                    "description": description
                }

                self.logger.info(f"Detected {name}: {coords} - {description}")

            confidence = data.get("confidence", "unknown")
            image_analysis = data.get("image_analysis", "No analysis provided")

            self.logger.info(f"Detection confidence: {confidence}")
            self.logger.debug(f"Image analysis: {image_analysis}")

            return True

        except json.JSONDecodeError as e:
            self.logger.error(f"JSON decode error: {e}")
            self.logger.debug(f"Response content: {content}")
            return False
        except Exception as e:
            self.logger.error(f"Error parsing response: {e}")
            return False

    def convert_to_absolute_coordinates(self, relative_coords: Tuple[int, int]) -> Tuple[int, int]:
        """Convert relative image coordinates to absolute screen coordinates"""
        rel_x, rel_y = relative_coords
        captcha_left, captcha_top = self.captcha_bbox[0], self.captcha_bbox[1]

        abs_x = captcha_left + rel_x
        abs_y = captcha_top + rel_y

        self.logger.debug(f"Coordinate conversion: ({rel_x}, {rel_y}) → ({abs_x}, {abs_y})")
        return (abs_x, abs_y)

    def get_slide_button_reference(self) -> Tuple[int, int]:
        """Get the slide button absolute coordinates as reference point"""
        self.logger.debug(f"Returning slide button reference: {self.slide_button_absolute}")
        return self.slide_button_absolute

    def calculate_distance_from_slide_button(self, target_coords: Tuple[int, int]) -> float:
        """Calculate distance from slide button to target coordinates"""
        slide_x, slide_y = self.slide_button_absolute
        target_x, target_y = target_coords

        distance = math.sqrt((target_x - slide_x)**2 + (target_y - slide_y)**2)

        self.logger.debug(f"Distance from slide button {self.slide_button_absolute} to {target_coords}: {distance:.2f} pixels")
        return distance

    def get_relative_position_to_slide_button(self, coords: Tuple[int, int]) -> Tuple[int, int]:
        """Get position relative to slide button (useful for drag calculations)"""
        slide_x, slide_y = self.slide_button_absolute
        coord_x, coord_y = coords

        relative_x = coord_x - slide_x
        relative_y = coord_y - slide_y

        self.logger.debug(f"Position relative to slide button: ({relative_x}, {relative_y})")
        return (relative_x, relative_y)

    def drag_slide_button_to_align_objects(self, validate: bool = True, safe_mode: bool = True) -> bool:
        """Drag from slide button to align the detected objects (proper captcha solving)"""
        self.logger.info("Starting slide button drag operation for object alignment")

        if len(self.detected_objects) < 2:
            self.logger.error("Need at least 2 detected objects to calculate drag")
            return False

        # Validate coordinates if requested
        if validate and not self.validate_detection_accuracy():
            self.logger.error("Detection validation failed - aborting drag for safety")
            return False

        self.logger.info("🖱️  Starting slide button drag operation...")

        # Safety check: ensure pyautogui failsafe is enabled
        if safe_mode:
            pyautogui.FAILSAFE = True
            self.logger.info("PyAutoGUI failsafe enabled (move mouse to top-left corner to abort)")

        # Get the two detected objects
        objects = list(self.detected_objects.values())
        obj1_rel = objects[0]["coordinates"]  # Source object (relative to captcha)
        obj2_rel = objects[1]["coordinates"]  # Target object (relative to captcha)

        # Convert to absolute coordinates
        obj1_abs = self.convert_to_absolute_coordinates(obj1_rel)
        obj2_abs = self.convert_to_absolute_coordinates(obj2_rel)

        # Calculate drag distance needed to align objects
        drag_distance = obj2_abs[0] - obj1_abs[0]  # Horizontal distance between objects

        # Calculate target position for slide button drag
        slide_start = self.slide_button_absolute
        slide_target_x = slide_start[0] + drag_distance
        slide_target_y = slide_start[1]  # Keep same Y coordinate
        slide_target = (slide_target_x, slide_target_y)

        self.logger.info(f"Slide button start: {slide_start}")
        self.logger.info(f"Object 1 position: {obj1_abs}")
        self.logger.info(f"Object 2 position: {obj2_abs}")
        self.logger.info(f"Required drag distance: {drag_distance} pixels")
        self.logger.info(f"Slide button target: {slide_target}")

        # Safety check: ensure coordinates are reasonable
        screen_width, screen_height = pyautogui.size()
        if not (0 <= slide_target[0] <= screen_width and 0 <= slide_target[1] <= screen_height):
            self.logger.error(f"Unsafe target coordinates {slide_target} - outside screen bounds")
            return False

        try:
            # Move to slide button starting position
            self.logger.info(f"Moving to slide button at {slide_start}")
            pyautogui.moveTo(slide_start[0], slide_start[1], duration=0.5)
            time.sleep(0.2)

            # Start drag operation
            self.logger.info("Starting drag from slide button...")
            pyautogui.mouseDown(button='left')
            time.sleep(0.1)

            # Drag to target position
            self.logger.info(f"Dragging to target position {slide_target}")
            pyautogui.moveTo(slide_target[0], slide_target[1], duration=1.5)
            time.sleep(0.2)

            # Release mouse
            pyautogui.mouseUp(button='left')
            self.logger.info("Drag operation completed successfully")

            # Store the drag information
            self.clicked_points = [slide_start, slide_target]

            self.logger.info(f"Successfully dragged slide button {drag_distance} pixels")
            return True

        except pyautogui.FailSafeException:
            self.logger.warning("PyAutoGUI failsafe triggered - stopping drag")
            pyautogui.mouseUp(button='left')  # Ensure mouse is released
            return False
        except Exception as e:
            self.logger.error(f"Error during drag operation: {e}")
            pyautogui.mouseUp(button='left')  # Ensure mouse is released
            return False

    def calculate_distance(self) -> Optional[float]:
        """Calculate the slide button drag distance and object alignment analysis"""
        self.logger.debug("Starting distance calculation")

        if len(self.clicked_points) < 2:
            self.logger.error("Need at least 2 points (slide start and end) to calculate distance")
            return None

        slide_start, slide_end = self.clicked_points[0], self.clicked_points[1]

        # Calculate slide button drag distance
        drag_distance = math.sqrt((slide_end[0] - slide_start[0])**2 + (slide_end[1] - slide_start[1])**2)
        horizontal_drag = abs(slide_end[0] - slide_start[0])
        vertical_drag = abs(slide_end[1] - slide_start[1])

        self.logger.info(f"Calculated drag distance: {drag_distance:.2f} pixels")
        self.logger.debug(f"Horizontal drag: {horizontal_drag:.2f}, Vertical drag: {vertical_drag:.2f}")

        # If we have detected objects, show the object alignment info
        if len(self.detected_objects) >= 2:
            objects = list(self.detected_objects.values())
            obj1_abs = self.convert_to_absolute_coordinates(objects[0]["coordinates"])
            obj2_abs = self.convert_to_absolute_coordinates(objects[1]["coordinates"])
            object_distance = math.sqrt((obj2_abs[0] - obj1_abs[0])**2 + (obj2_abs[1] - obj1_abs[1])**2)
            self.logger.debug(f"Distance between detected objects: {object_distance:.2f} pixels")

        return drag_distance

    def run_automated_measurement(self, target_objects: str = "two main objects") -> Optional[float]:
        """Run the complete automated distance measurement process"""
        self.logger.info("Starting Automated Distance Measurement Process")
        self.logger.info("=" * 60)

        try:
            # Step 1: Capture screenshot
            self.logger.info("Step 1: Capturing screenshot...")
            image_path = self.capture_screenshot()

            # Step 2: Detect objects with AI
            self.logger.info("Step 2: Detecting objects with AI Vision...")
            response = self.detect_objects_with_grok(image_path, target_objects)
            if not response:
                self.logger.error("Failed to get AI response")
                return None

            # Step 3: Parse detection results
            self.logger.info("Step 3: Parsing detection results...")
            if not self.parse_detection_response(response):
                self.logger.error("Failed to parse detection results")
                return None

            # Step 4: Drag slide button to align objects
            self.logger.info("🖱️  Step 4: Dragging slide button to align objects...")
            if not self.drag_slide_button_to_align_objects():
                self.logger.error("Failed to drag slide button")
                return None

            # Step 5: Calculate distance
            self.logger.info("Step 5: Calculating distance...")
            distance = self.calculate_distance()

            if distance is not None:
                self.logger.info("=" * 60)
                self.logger.info("🎉 AUTOMATED MEASUREMENT COMPLETED SUCCESSFULLY!")
                self.logger.info(f"Final Distance: {distance:.2f} pixels")
                self.logger.info("=" * 60)
            else:
                self.logger.error("Distance calculation failed")

            return distance

        except Exception as e:
            self.logger.error(f"Error in automated measurement process: {str(e)}")
            return None


    def run_continuous_monitoring(self, target_objects: str = "two main objects", interval: int = 5) -> None:
        """Run continuous distance monitoring with specified interval"""
        self.logger.info(f"Starting continuous monitoring (every {interval} seconds)")
        self.logger.info("Press Ctrl+C to stop monitoring")

        try:
            measurement_count = 0
            while True:
                measurement_count += 1
                self.logger.info(f"📊 Measurement #{measurement_count} - {time.strftime('%H:%M:%S')}")

                distance = self.run_automated_measurement(target_objects)

                if distance is not None:
                    self.logger.info(f"Measurement #{measurement_count}: {distance:.2f} pixels")
                else:
                    self.logger.warning(f"Measurement #{measurement_count}: Failed")

                self.logger.debug(f"Waiting {interval} seconds for next measurement...")
                time.sleep(interval)

        except KeyboardInterrupt:
            self.logger.info(f"⏹️ Monitoring stopped. Completed {measurement_count} measurements.")

    def save_measurement_log(self, distance: float, filename: str = "distance_log.txt") -> None:
        """Save measurement results to a log file"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"{timestamp} - Distance: {distance:.2f} pixels - Objects: {list(self.detected_objects.keys())}\n"

        try:
            with open(filename, "a") as f:
                f.write(log_entry)
            self.logger.info(f"Measurement logged to {filename}")
        except Exception as e:
            self.logger.error(f"Failed to save log: {e}")

    def validate_detection_accuracy(self) -> bool:
        """Validate that detected coordinates are within reasonable bounds"""
        self.logger.debug("Starting detection accuracy validation")

        captcha_width = self.captcha_bbox[2] - self.captcha_bbox[0]
        captcha_height = self.captcha_bbox[3] - self.captcha_bbox[1]

        for name, obj_data in self.detected_objects.items():
            x, y = obj_data["coordinates"]

            # Check if coordinates are within captcha bounds
            if x < 0 or x >= captcha_width or y < 0 or y >= captcha_height:
                self.logger.warning(f"Warning: {name} coordinates ({x}, {y}) are outside captcha bounds")
                return False

            # Check if coordinates are not at extreme edges (likely detection errors)
            margin = 10
            if x < margin or x > captcha_width - margin or y < margin or y > captcha_height - margin:
                self.logger.warning(f"Warning: {name} coordinates ({x}, {y}) are very close to edges")

        self.logger.debug("Detection coordinates validation passed")
        return True



    def integrate_with_captcha_solver(self) -> Optional[Dict[str, Any]]:
        """Integration method for use with existing captcha solver - returns comprehensive data"""
        self.logger.info("🔗 Integration mode: Using captcha solver infrastructure")
        self.logger.info(f"Using slide button reference: {self.slide_button_absolute}")

        try:
            # Use the same screenshot capture method as captcha solver
            image_path = self.capture_screenshot()

            # Detect objects using the same AI model
            response = self.detect_objects_with_grok(image_path, "two main objects")
            if not response:
                self.logger.error("Failed to get AI response for captcha solver integration")
                return None

            # Parse and validate
            if not self.parse_detection_response(response):
                self.logger.error("Failed to parse detection response for captcha solver integration")
                return None

            # Calculate comprehensive measurements for captcha solver use
            if len(self.detected_objects) >= 2:
                objects = list(self.detected_objects.values())
                coord1_rel = objects[0]["coordinates"]  # Relative to captcha
                coord2_rel = objects[1]["coordinates"]  # Relative to captcha

                # Convert to absolute coordinates
                coord1_abs = self.convert_to_absolute_coordinates(coord1_rel)
                coord2_abs = self.convert_to_absolute_coordinates(coord2_rel)

                # Calculate distance between objects
                distance = math.sqrt((coord2_rel[0] - coord1_rel[0])**2 + (coord2_rel[1] - coord1_rel[1])**2)

                # Calculate slide button relationships
                slide_to_obj1 = self.calculate_distance_from_slide_button(coord1_abs)
                slide_to_obj2 = self.calculate_distance_from_slide_button(coord2_abs)

                # Calculate drag distance needed for alignment
                drag_distance = abs(coord2_abs[0] - coord1_abs[0])  # Horizontal drag

                # Determine drag direction
                drag_direction = "right" if coord2_abs[0] > coord1_abs[0] else "left"

                # Create comprehensive result for captcha solver
                result = {
                    "distance_pixels": round(distance, 2),
                    "object1": {
                        "relative_coords": coord1_rel,
                        "absolute_coords": coord1_abs,
                        "distance_from_slide": round(slide_to_obj1, 2),
                        "name": list(self.detected_objects.keys())[0]
                    },
                    "object2": {
                        "relative_coords": coord2_rel,
                        "absolute_coords": coord2_abs,
                        "distance_from_slide": round(slide_to_obj2, 2),
                        "name": list(self.detected_objects.keys())[1]
                    },
                    "slide_button": self.slide_button_absolute,
                    "drag_info": {
                        "distance": round(drag_distance, 2),
                        "direction": drag_direction,
                        "start_point": coord1_abs,
                        "target_point": coord2_abs
                    },
                    "captcha_region": self.captcha_bbox
                }

                self.logger.info("CAPTCHA SOLVER INTEGRATION RESULTS:")
                self.logger.info(f"Object distance: {distance:.2f} pixels")
                self.logger.info(f"Slide button: {self.slide_button_absolute}")
                self.logger.info(f"🖱️ Required drag: {drag_distance:.2f} pixels {drag_direction}")
                self.logger.info(f"📊 Object 1 ({result['object1']['name']}): {coord1_abs}")
                self.logger.info(f"📊 Object 2 ({result['object2']['name']}): {coord2_abs}")

                return result
            else:
                self.logger.error("Insufficient objects detected for captcha solver integration")
                return None

        except Exception as e:
            self.logger.error(f"Error in captcha solver integration: {str(e)}")
            return None

    def get_captcha_solver_measurements(self) -> Optional[Dict[str, float]]:
        """Get measurements in format compatible with captcha solver distance calculations"""
        integration_result = self.integrate_with_captcha_solver()

        if integration_result:
            return {
                "total_distance": integration_result["distance_pixels"],
                "drag_distance": integration_result["drag_info"]["distance"],
                "object1_from_slide": integration_result["object1"]["distance_from_slide"],
                "object2_from_slide": integration_result["object2"]["distance_from_slide"],
                "horizontal_gap": abs(integration_result["object2"]["absolute_coords"][0] -
                                    integration_result["object1"]["absolute_coords"][0]),
                "vertical_gap": abs(integration_result["object2"]["absolute_coords"][1] -
                                  integration_result["object1"]["absolute_coords"][1])
            }

        return None



    def create_measurement_report(self, distance: float) -> Dict[str, Any]:
        """Create a detailed measurement report with slide button reference"""
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "distance_pixels": round(distance, 2),
            "objects_detected": len(self.detected_objects),
            "object_details": {},
            "slide_button_reference": self.slide_button_absolute,
            "captcha_region": self.captcha_bbox,
            "clicked_points": self.clicked_points,
            "slide_button_analysis": {}
        }

        # Add object details with slide button relationships
        for name, obj_data in self.detected_objects.items():
            abs_coords = self.convert_to_absolute_coordinates(obj_data["coordinates"])
            distance_from_slide = self.calculate_distance_from_slide_button(abs_coords)
            relative_to_slide = self.get_relative_position_to_slide_button(abs_coords)

            report["object_details"][name] = {
                "coordinates": obj_data["coordinates"],
                "description": obj_data["description"],
                "absolute_coordinates": abs_coords,
                "distance_from_slide_button": round(distance_from_slide, 2),
                "relative_to_slide_button": relative_to_slide
            }

        # Add slide button analysis if we have clicked points
        if len(self.clicked_points) >= 2:
            drag_distance = abs(self.clicked_points[1][0] - self.clicked_points[0][0])
            drag_direction = "right" if self.clicked_points[1][0] > self.clicked_points[0][0] else "left"

            report["slide_button_analysis"] = {
                "required_drag_distance": round(drag_distance, 2),
                "drag_direction": drag_direction,
                "start_relative_to_slide": self.get_relative_position_to_slide_button(self.clicked_points[0]),
                "end_relative_to_slide": self.get_relative_position_to_slide_button(self.clicked_points[1])
            }

        return report


def solve_captcha_automatically(captcha_bbox=None, slide_button_coords=None, objects=None, max_attempts=1):
    """
    Automatic captcha solver function for GMX integration.

    Args:
        captcha_bbox: Tuple of (left, top, right, bottom) for captcha region
        slide_button_coords: Tuple of (x, y) for slide button position
        objects: Description of objects to detect and align
        max_attempts: Maximum number of attempts before giving up

    Returns:
        bool: True if captcha solved successfully, False otherwise
    """
    # Setup logging for standalone function
    setup_logging()
    logger = logging.getLogger("solve_captcha_automatically")

    logger.info("Starting automatic captcha solver...")

    for attempt in range(1, max_attempts + 1):
        logger.info(f"Attempt {attempt}/{max_attempts}")

        try:
            # Initialize tool with custom coordinates if provided
            tool = AutomatedDistanceTool()

            if captcha_bbox:
                tool.captcha_bbox = captcha_bbox
                logger.info(f"Using custom captcha region: {captcha_bbox}")

            if slide_button_coords:
                tool.slide_button_absolute = slide_button_coords
                logger.info(f"Using custom slide button: {slide_button_coords}")

            # Run automated measurement and drag
            distance = tool.run_automated_measurement(objects)

            if distance is not None:
                logger.info(f"Captcha solved successfully on attempt {attempt}")
                logger.info(f"Drag distance: {distance:.2f} pixels")
                return True
            else:
                logger.warning(f"Attempt {attempt} failed")
                if attempt < max_attempts:
                    wait_time = uniform(12.0, 15.0)
                    logger.info(f"Waiting {wait_time:.1f} seconds before retry...")
                    time.sleep(wait_time)

        except Exception as e:
            logger.error(f"Error on attempt {attempt}: {e}")
            if attempt < max_attempts:
                wait_time = uniform(12.0, 15.0)
                logger.info(f"Waiting {wait_time:.1f} seconds before retry...")
                time.sleep(wait_time)

    logger.error(f"Failed to solve captcha after {max_attempts} attempts")
    return False


def get_captcha_solution_data(captcha_bbox=None, slide_button_coords=None, objects=None):
    """
    Get captcha solution data without performing the drag (for manual implementation).

    Args:
        captcha_bbox: Tuple of (left, top, right, bottom) for captcha region
        slide_button_coords: Tuple of (x, y) for slide button position
        objects: Description of objects to detect

    Returns:
        dict: Solution data with drag coordinates and distance, or None if failed
    """
    # Setup logging for standalone function
    setup_logging()
    logger = logging.getLogger("get_captcha_solution_data")

    logger.info("🔍 Analyzing captcha for solution data...")

    try:
        # Initialize tool
        tool = AutomatedDistanceTool()

        if captcha_bbox:
            tool.captcha_bbox = captcha_bbox
            logger.debug(f"Using custom captcha bbox: {captcha_bbox}")

        if slide_button_coords:
            tool.slide_button_absolute = slide_button_coords
            logger.debug(f"Using custom slide button coords: {slide_button_coords}")

        # Capture and analyze with specified objects
        image_path = tool.capture_screenshot()
        response = tool.detect_objects_with_grok(image_path, objects)

        if not response or not tool.parse_detection_response(response):
            logger.error("Could not detect objects")
            return {'success': False, 'error': 'Object detection failed'}

        # Get comprehensive measurements
        measurements = tool.integrate_with_captcha_solver()

        if measurements:
            solution_data = {
                'success': True,
                'drag_distance': measurements['drag_info']['distance'],
                'drag_direction': measurements['drag_info']['direction'],
                'slide_button_start': measurements['slide_button'],
                'slide_button_target': (
                    measurements['slide_button'][0] + (
                        measurements['drag_info']['distance'] if measurements['drag_info']['direction'] == 'right'
                        else -measurements['drag_info']['distance']
                    ),
                    measurements['slide_button'][1]
                ),
                'object1_coords': measurements['object1']['absolute_coords'],
                'object2_coords': measurements['object2']['absolute_coords'],
                'distance_between_objects': measurements['distance_pixels']
            }

            logger.info("Captcha solution data generated successfully")
            logger.info(f"Drag from {solution_data['slide_button_start']} to {solution_data['slide_button_target']}")
            logger.info(f"Drag distance: {solution_data['drag_distance']} pixels {solution_data['drag_direction']}")

            return solution_data
        else:
            logger.error("Could not analyze captcha")
            return {'success': False, 'error': 'Analysis failed'}

    except Exception as e:
        logger.error(f"Error analyzing captcha: {e}")
        return {'success': False, 'error': str(e)}


def main():
    """Main function for command-line usage"""
    # Setup logging for main function
    setup_logging()
    logger = logging.getLogger("main")

    parser = argparse.ArgumentParser(description="Automated Captcha Solver")
    parser.add_argument("--mode", choices=["auto", "analyze", "monitor"], default="auto",
                       help="Mode: auto (solve captcha), analyze (get solution data), monitor (continuous)")
    parser.add_argument("--objects", type=str, default="fox and robot",
                       help="Description of objects to detect (e.g., 'fox and robot')")
    parser.add_argument("--attempts", type=int, default=3,
                       help="Maximum attempts for captcha solving")
    parser.add_argument("--captcha-bbox", type=str, default=None,
                       help="Captcha bounding box as 'left,top,right,bottom'")
    parser.add_argument("--slide-coords", type=str, default=None,
                       help="Slide button coordinates as 'x,y'")
    parser.add_argument("--interval", type=int, default=10,
                       help="Interval in seconds for monitor mode")

    args = parser.parse_args()

    logger.info(f"Starting AI Captcha Solver in {args.mode} mode")
    logger.debug(f"Arguments: {vars(args)}")

    # Parse coordinates if provided
    captcha_bbox = None
    if args.captcha_bbox:
        try:
            coords = [int(x.strip()) for x in args.captcha_bbox.split(',')]
            if len(coords) == 4:
                captcha_bbox = tuple(coords)
                logger.debug(f"Parsed captcha bbox: {captcha_bbox}")
        except:
            logger.error("Invalid captcha bbox format. Use: left,top,right,bottom")
            return

    slide_coords = None
    if args.slide_coords:
        try:
            coords = [int(x.strip()) for x in args.slide_coords.split(',')]
            if len(coords) == 2:
                slide_coords = tuple(coords)
                logger.debug(f"Parsed slide coords: {slide_coords}")
        except:
            logger.error("Invalid slide coordinates format. Use: x,y")
            return

    if args.mode == "auto":
        # Solve captcha automatically
        logger.info("Running automatic captcha solving mode")
        success = solve_captcha_automatically(
            captcha_bbox=captcha_bbox,
            slide_button_coords=slide_coords,
            objects=args.objects,
            max_attempts=args.attempts
        )
        if success:
            logger.info("Captcha solved successfully!")
        else:
            logger.error("Failed to solve captcha")

    elif args.mode == "analyze":
        # Get solution data without solving
        logger.info("Running captcha analysis mode")
        data = get_captcha_solution_data(
            captcha_bbox=captcha_bbox,
            slide_button_coords=slide_coords,
            objects=args.objects
        )
        if data['success']:
            logger.info("Solution data generated successfully")
        else:
            logger.error(f"Analysis failed: {data.get('error', 'Unknown error')}")

    elif args.mode == "monitor":
        # Continuous monitoring
        logger.info(f"Starting continuous captcha monitoring (every {args.interval} seconds)")
        logger.info("Press Ctrl+C to stop")

        try:
            count = 0
            while True:
                count += 1
                logger.info(f"Check #{count} - {time.strftime('%H:%M:%S')}")

                success = solve_captcha_automatically(
                    captcha_bbox=captcha_bbox,
                    slide_button_coords=slide_coords,
                    objects=args.objects,
                    max_attempts=1
                )

                if success:
                    logger.info(f"Check #{count}: Captcha solved")
                else:
                    logger.warning(f"Check #{count}: No captcha or failed")

                logger.debug(f"Waiting {args.interval} seconds...")
                time.sleep(args.interval)

        except KeyboardInterrupt:
            logger.info(f"⏹️ Monitoring stopped after {count} checks")


if __name__ == "__main__":
    main()