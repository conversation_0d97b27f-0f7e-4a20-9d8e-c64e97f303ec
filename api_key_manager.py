#!/usr/bin/env python3
"""
OpenRouter API Key Manager with Rotation and Fallback

This module provides centralized API key management for OpenRouter requests
with automatic rotation and fallback functionality.

Features:
- Multiple API key support with automatic rotation
- Fallback to other keys when one fails
- Thread-safe operations for concurrent usage
- Comprehensive error handling and logging
- Request retry logic with exponential backoff
- Built-in configuration management with environment variable support
"""

import os
import json
import requests
import time
import threading
from typing import List, Optional, Dict, Any
from random import uniform


class OpenRouterAPIManager:
    """
    Centralized API key manager for OpenRouter requests with rotation and fallback.
    """

    # Default API keys (can be overridden)
    DEFAULT_API_KEYS = [
        "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",
        "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"
    ]

    DEFAULT_BASE_URL = "https://openrouter.ai/api/v1"
    DEFAULT_TIMEOUT = 30
    DEFAULT_MAX_RETRIES = None  # Will use number of API keys

    def __init__(self, api_keys: Optional[List[str]] = None, base_url: Optional[str] = None,
                 timeout: Optional[int] = None, config_file: Optional[str] = None):
        # Load configuration from various sources
        config = self._load_config(config_file, api_keys, base_url, timeout)

        self.api_keys = config["api_keys"]
        self.base_url = config["base_url"]
        self.timeout = config["timeout"]
        self.max_retries = config["max_retries"]

        if not self.api_keys:
            raise ValueError("At least one API key must be provided")

        self.current_key_index = 0
        self._lock = threading.Lock()  # Thread safety for concurrent usage

        # Track key performance for intelligent rotation
        self.key_stats = {i: {"success": 0, "failures": 0, "last_used": 0} for i in range(len(self.api_keys))}

        print(f"OpenRouter API Manager initialized with {len(self.api_keys)} keys")

    def _load_config(self, config_file: Optional[str] = None,
                    api_keys: Optional[List[str]] = None,
                    base_url: Optional[str] = None,
                    timeout: Optional[int] = None) -> Dict[str, Any]:
        """Load configuration from various sources"""
        config = {
            "api_keys": self.DEFAULT_API_KEYS.copy(),
            "base_url": self.DEFAULT_BASE_URL,
            "timeout": self.DEFAULT_TIMEOUT,
            "max_retries": self.DEFAULT_MAX_RETRIES
        }

        # 1. Load from config file if provided
        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    file_config = json.load(f)
                    config.update(file_config)
                print(f" Loaded configuration from {config_file}")
            except Exception as e:
                print(f"️ Failed to load config file {config_file}: {e}")

        # 2. Override with environment variables
        env_keys = os.getenv('OPENROUTER_API_KEYS')
        if env_keys:
            # Support comma-separated keys in environment variable
            config["api_keys"] = [key.strip() for key in env_keys.split(',') if key.strip()]
            print(f" Loaded {len(config['api_keys'])} API keys from environment")

        env_base_url = os.getenv('OPENROUTER_BASE_URL')
        if env_base_url:
            config["base_url"] = env_base_url
            print(f" Using base URL from environment: {env_base_url}")

        env_timeout = os.getenv('OPENROUTER_TIMEOUT')
        if env_timeout:
            try:
                config["timeout"] = int(env_timeout)
                print(f" Using timeout from environment: {env_timeout}s")
            except ValueError:
                print(f"️ Invalid timeout in environment: {env_timeout}")

        # 3. Override with direct parameters (highest priority)
        if api_keys is not None:
            config["api_keys"] = api_keys.copy()
        if base_url is not None:
            config["base_url"] = base_url
        if timeout is not None:
            config["timeout"] = timeout

        return config

    def get_current_api_key(self) -> str:
        """Get the current API key (thread-safe)"""
        with self._lock:
            return self.api_keys[self.current_key_index]
    
    def rotate_api_key(self) -> None:
        """Rotate to the next API key (thread-safe)"""
        with self._lock:
            self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
            print(f"Rotated to API key #{self.current_key_index + 1}")
    
    def get_best_key_index(self) -> int:
        """Get the index of the best performing key based on success rate"""
        with self._lock:
            best_index = 0
            best_score = -1
            
            for i, stats in self.key_stats.items():
                total_requests = stats["success"] + stats["failures"]
                if total_requests == 0:
                    score = 1.0  # New key gets priority
                else:
                    success_rate = stats["success"] / total_requests
                    # Factor in recency (prefer recently successful keys)
                    recency_factor = max(0.1, 1.0 - (time.time() - stats["last_used"]) / 3600)
                    score = success_rate * recency_factor
                
                if score > best_score:
                    best_score = score
                    best_index = i
            
            return best_index
    
    def update_key_stats(self, key_index: int, success: bool) -> None:
        """Update statistics for a specific key"""
        with self._lock:
            if success:
                self.key_stats[key_index]["success"] += 1
            else:
                self.key_stats[key_index]["failures"] += 1
            self.key_stats[key_index]["last_used"] = time.time()
    
    def make_request(self,
                    endpoint: str,
                    method: str = "POST",
                    payload: Optional[Dict[str, Any]] = None,
                    additional_headers: Optional[Dict[str, str]] = None,
                    max_retries: Optional[int] = None,
                    timeout: Optional[int] = None) -> Optional[Dict[str, Any]]:

        if max_retries is None:
            max_retries = self.max_retries or len(self.api_keys)

        if timeout is None:
            timeout = self.timeout
        
        original_key_index = self.current_key_index
        
        for attempt in range(max_retries):
            # Use the best performing key for the first attempt
            if attempt == 0:
                key_index = self.get_best_key_index()
                with self._lock:
                    self.current_key_index = key_index
            
            current_key = self.get_current_api_key()
            
            # Prepare headers
            headers = {
                "Authorization": f"Bearer {current_key}",
                "Content-Type": "application/json"
            }
            if additional_headers:
                headers.update(additional_headers)
            
            try:
                print(f"Using API key #{self.current_key_index + 1} (attempt {attempt + 1}/{max_retries})")
                
                # Make the request
                url = f"{self.base_url}{endpoint}"
                if method.upper() == "GET":
                    response = requests.get(url, headers=headers, timeout=timeout)
                elif method.upper() == "POST":
                    response = requests.post(url, headers=headers, json=payload, timeout=timeout)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                print(f" Response status: {response.status_code}")
                
                # Handle successful response
                if response.status_code == 200:
                    if not response.text.strip():
                        print("Empty response from API")
                        self.update_key_stats(self.current_key_index, False)
                        self.rotate_api_key()
                        continue
                    
                    # Success - update stats and rotate for load distribution
                    self.update_key_stats(self.current_key_index, True)
                    self.rotate_api_key()
                    return response.json()
                
                # Handle specific error codes
                elif response.status_code == 401:
                    print(f"API key #{self.current_key_index + 1} authentication failed")
                    self.update_key_stats(self.current_key_index, False)
                    self.rotate_api_key()
                    continue
                
                elif response.status_code == 429:
                    print(f"API key #{self.current_key_index + 1} rate limited")
                    self.update_key_stats(self.current_key_index, False)
                    self.rotate_api_key()
                    # Add delay for rate limiting
                    time.sleep(uniform(1.0, 3.0))
                    continue
                
                elif response.status_code >= 500:
                    print(f"Server error {response.status_code}, trying next key")
                    self.update_key_stats(self.current_key_index, False)
                    self.rotate_api_key()
                    continue
                
                else:
                    print(f"API Error {response.status_code}: {response.text}")
                    self.update_key_stats(self.current_key_index, False)
                    self.rotate_api_key()
                    continue
                    
            except requests.exceptions.Timeout:
                print(f"Request timeout with API key #{self.current_key_index + 1}")
                self.update_key_stats(self.current_key_index, False)
                self.rotate_api_key()
                continue
                
            except requests.exceptions.RequestException as e:
                print(f"Network error with API key #{self.current_key_index + 1}: {e}")
                self.update_key_stats(self.current_key_index, False)
                self.rotate_api_key()
                continue
                
            except Exception as e:
                print(f"Unexpected error with API key #{self.current_key_index + 1}: {e}")
                self.update_key_stats(self.current_key_index, False)
                self.rotate_api_key()
                continue
        
        # All keys failed, reset to original key
        with self._lock:
            self.current_key_index = original_key_index
        print(f"All {max_retries} API key attempts failed")
        return None
    
    def check_credits(self, min_threshold: float = 1.0) -> tuple:
        """
        Check OpenRouter API credits with fallback support.
        
        Args:
            min_threshold: Minimum credit threshold required
            
        Returns:
            tuple: (success: bool, credits: float, message: str)
        """
        response = self.make_request("/credits", method="GET")
        
        if not response:
            return False, 0.0, "Failed to check credits - all API keys failed"
        
        try:
            # Extract credit information from response
            if 'data' in response:
                credit_info = response['data']
                
                if isinstance(credit_info, dict):
                    total_credits = float(credit_info.get('total_credits', 0.0))
                    total_usage = float(credit_info.get('total_usage', 0.0))
                    available_credits = total_credits - total_usage
                    
                    print(f"OpenRouter Credits Available: ${available_credits:.4f}")
                    
                    if available_credits >= min_threshold:
                        return True, available_credits, "Sufficient credits available"
                    else:
                        error_msg = f"Insufficient credits: ${available_credits:.4f} < ${min_threshold:.2f} required"
                        return False, available_credits, error_msg
                else:
                    return False, 0.0, "Unexpected credit info format"
            else:
                return False, 0.0, "Could not determine credit balance from API response"
                
        except Exception as e:
            return False, 0.0, f"Error parsing credit response: {str(e)}"
    
    def get_stats(self) -> Dict[str, Any]:
        """Get performance statistics for all API keys"""
        with self._lock:
            stats = {}
            for i, key_stats in self.key_stats.items():
                total = key_stats["success"] + key_stats["failures"]
                success_rate = key_stats["success"] / total if total > 0 else 0.0
                stats[f"key_{i+1}"] = {
                    "success_rate": f"{success_rate:.2%}",
                    "total_requests": total,
                    "last_used": key_stats["last_used"]
                }
            return stats

    def add_api_key(self, api_key: str) -> None:
        """Add a new API key to the configuration"""
        with self._lock:
            if api_key not in self.api_keys:
                self.api_keys.append(api_key)
                # Initialize stats for new key
                new_index = len(self.api_keys) - 1
                self.key_stats[new_index] = {"success": 0, "failures": 0, "last_used": 0}
                print(f"Added new API key (total: {len(self.api_keys)})")
            else:
                print("️ API key already exists in configuration")

    def remove_api_key(self, api_key: str) -> bool:
        """Remove an API key from the configuration"""
        with self._lock:
            if api_key in self.api_keys:
                index = self.api_keys.index(api_key)
                self.api_keys.remove(api_key)

                # Rebuild key_stats with new indices
                new_stats = {}
                for i in range(len(self.api_keys)):
                    old_index = index if i >= index else i
                    new_stats[i] = self.key_stats.get(old_index, {"success": 0, "failures": 0, "last_used": 0})
                self.key_stats = new_stats

                # Adjust current key index if necessary
                if self.current_key_index >= len(self.api_keys):
                    self.current_key_index = 0

                print(f" Removed API key (remaining: {len(self.api_keys)})")
                return True
            else:
                print("️ API key not found in configuration")
                return False

    def validate_config(self) -> bool:
        """Validate the current configuration"""
        issues = []

        # Check API keys
        if not self.api_keys:
            issues.append("No API keys configured")
        else:
            for i, key in enumerate(self.api_keys):
                if not key or not key.startswith("sk-or-v1-"):
                    issues.append(f"API key {i+1} appears invalid: {key[:20]}...")

        # Check base URL
        if not self.base_url:
            issues.append("No base URL configured")
        elif not self.base_url.startswith("https://"):
            issues.append("Base URL should use HTTPS")

        # Check timeout
        if self.timeout <= 0:
            issues.append("Timeout must be positive")

        if issues:
            print(" Configuration validation failed:")
            for issue in issues:
                print(f"   • {issue}")
            return False
        else:
            print(" Configuration validation passed")
            return True

    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the current configuration"""
        return {
            "api_keys_count": len(self.api_keys),
            "api_keys_preview": [key[:20] + "..." for key in self.api_keys],
            "base_url": self.base_url,
            "timeout": self.timeout,
            "max_retries": self.max_retries
        }


# Global instance for easy access
_global_api_manager = None

def get_api_manager(config_file: Optional[str] = None) -> OpenRouterAPIManager:
    """Get the global API manager instance"""
    global _global_api_manager
    if _global_api_manager is None:
        # Create API manager with built-in configuration loading
        _global_api_manager = OpenRouterAPIManager(config_file=config_file)
    return _global_api_manager

def initialize_api_manager(api_keys: List[str], base_url: Optional[str] = None) -> OpenRouterAPIManager:
    """Initialize the global API manager with custom keys"""
    global _global_api_manager
    _global_api_manager = OpenRouterAPIManager(api_keys=api_keys, base_url=base_url)
    return _global_api_manager

def load_config_from_file(config_file: str) -> OpenRouterAPIManager:
    """Load configuration from a specific file and create API manager"""
    return OpenRouterAPIManager(config_file=config_file)


# Example configuration file format
EXAMPLE_CONFIG = {
    "api_keys": [
        "sk-or-v1-your-first-api-key-here",
        "sk-or-v1-your-second-api-key-here"
    ],
    "base_url": "https://openrouter.ai/api/v1",
    "timeout": 30,
    "max_retries": None
}


if __name__ == "__main__":
    # Demo the API manager system
    print("OpenRouter API Manager Demo")
    print("=" * 40)

    # Create API manager instance
    api_manager = get_api_manager()

    # Show current configuration
    print("Current Configuration:")
    summary = api_manager.get_summary()
    for key, value in summary.items():
        print(f"   {key}: {value}")

    print()

    # Validate configuration
    print(" Validating Configuration:")
    api_manager.validate_config()

    print()

    # Show example config file format
    print(" Example config.json format:")
    print(json.dumps(EXAMPLE_CONFIG, indent=2))
